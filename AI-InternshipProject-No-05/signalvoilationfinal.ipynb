{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true, "base_uri": "https://localhost:8080/", "height": 1000}, "id": "7LUGnbbsOLbG", "outputId": "23615692-7268-41ed-cd1f-39209d332ec6"}, "outputs": [], "source": ["!pip install ultralytics opencv-python\n", "\n", "import cv2\n", "import os\n", "import numpy as np\n", "from ultralytics import YOLO\n", "from google.colab.patches import cv2_imshow\n", "\n", "# Load YOLO model\n", "model = YOLO(\"yolov8n.pt\")\n", "\n", "# Input video\n", "video_path = \"/content/tr.mp4\"   # apni video ka path do\n", "cap = cv2.VideoCapture(video_path)\n", "\n", "# Output video\n", "fourcc = cv2.VideoWriter_fourcc(*'XVID')\n", "out = cv2.VideoWriter(\"output.avi\", fourcc, 30, (int(cap.get(3)), int(cap.get(4))))\n", "\n", "# Folder for snapshots\n", "os.makedirs(\"violations\", exist_ok=True)\n", "\n", "# ========================\n", "# Traffic Signal Detection (your logic)\n", "# ========================\n", "# Update traffic_light_roi to be within frame boundaries\n", "# Adjust values if needed\n", "H, W = 800, 100  # Initialize H and W\n", "roi_w, roi_h = 90, 160   # width, height of ROI box\n", "roi_x = W - 60          # 100 px from right edge\n", "roi_y = 0    # will be set later\n", "\n", "traffic_light_roi = (roi_x, roi_y, roi_w, roi_h)\n", "\n", "def get_light_color(frame):\n", "    global traffic_light_roi, <PERSON>, W # Declare as global\n", "\n", "    H, W = frame.shape[:2]\n", "    roi_x = W - 250       # 100 px from right edge\n", "    roi_y = int(H/3) - 160  # roughly 1/3rd from top\n", "    traffic_light_roi = (roi_x, roi_y, roi_w, roi_h)\n", "\n", "    x, y, w, h = traffic_light_roi\n", "    # Ensure ROI is within frame boundaries\n", "    x = max(0, x)\n", "    y = max(0, y)\n", "    w = min(w, frame.shape[1] - x)\n", "    h = min(h, frame.shape[0] - y)\n", "\n", "    roi = frame[y:y+h, x:x+w]\n", "    # Check if ROI is empty\n", "    if roi.size == 0:\n", "        return \"UNKNOWN\"\n", "\n", "    hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)\n", "\n", "    # Red range\n", "    lower_red1 = np.array([0, 100, 100])\n", "    upper_red1 = np.array([10, 255, 255])\n", "    lower_red2 = np.array([160, 100, 100])\n", "    upper_red2 = np.array([180, 255, 255])\n", "\n", "    # Yellow range\n", "    lower_yellow = np.array([15, 100, 100])\n", "    upper_yellow = np.array([35, 255, 255])\n", "\n", "    # Green range\n", "    lower_green = np.array([40, 50, 50])\n", "    upper_green = np.array([90, 255, 255])\n", "\n", "    # Create masks\n", "    mask_red = cv2.inRange(hsv, lower_red1, upper_red1) | cv2.inRange(hsv, lower_red2, upper_red2)\n", "    mask_yellow = cv2.inRange(hsv, lower_yellow, upper_yellow)\n", "    mask_green = cv2.inRange(hsv, lower_green, upper_green)\n", "\n", "    # Count pixels\n", "    red_pixels = cv2.countNonZero(mask_red)\n", "    yellow_pixels = cv2.countNonZero(mask_yellow)\n", "    green_pixels = cv2.countNonZero(mask_green)\n", "\n", "    if red_pixels > yellow_pixels and red_pixels > green_pixels and red_pixels > 50:\n", "        return \"RED\"\n", "    elif yellow_pixels > red_pixels and yellow_pixels > green_pixels and yellow_pixels > 50:\n", "        return \"YELLOW\"\n", "    elif green_pixels > red_pixels and green_pixels > yellow_pixels and green_pixels > 50:\n", "        return \"GREEN\"\n", "    else:\n", "        return \"UNKNOWN\"\n", "# ========================\n", "# Main Loop\n", "# ========================\n", "STOP_LINE_Y = 700  # apni video ke hisaab se <PERSON> karo\n", "frame_count = 0\n", "violation_count = 0\n", "captured_ids = set()\n", "while cap.isOpened():\n", "    ret, frame = cap.read()\n", "    if not ret:\n", "        break\n", "\n", "    frame_count += 10\n", "\n", "    # --- Detect traffic signal ---\n", "    signal_state = get_light_color(frame)\n", "\n", "    results = model.track(frame, persist=True, verbose=False)   # track instead of predict\n", "\n", "    for r in results:\n", "        for box in r.boxes:\n", "            cls_id = int(box.cls[0])\n", "            label = model.names[cls_id]\n", "\n", "            if label in [\"car\", \"truck\", \"bus\", \"motorbike\"]:\n", "                x1, y1, x2, y2 = map(int, box.xyxy[0])\n", "                cy = (y1 + y2) // 2  # center Y\n", "\n", "                color = (0, 255, 0)  # default safe\n", "\n", "                # Tracking ID (unique for each vehicle)\n", "                track_id = int(box.id[0]) if box.id is not None else None\n", "\n", "                # 🚦 Violation only if RED and vehicle crosses line\n", "                if signal_state == \"RED\" and cy < STOP_LINE_Y:\n", "                    color = (0, 0, 255)\n", "\n", "                    # ✅ Only save once per vehicle\n", "                    if track_id is not None and track_id not in captured_ids:\n", "                        violation_count += 1\n", "                        captured_ids.add(track_id)  # remember this vehicle\n", "                        cv2.imwrite(f\"violations/violation_{violation_count}.jpg\", frame)\n", "\n", "                # Draw box + label\n", "                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)\n", "                cv2.putText(frame, label, (x1, y1 - 5),\n", "                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)\n", "\n", "    # --- Draw ROI for traffic light ---\n", "    x, y, w, h = traffic_light_roi\n", "    cv2.rectangle(frame, (x, y), (x+w, y+h), (255, 255, 0), 2)\n", "\n", "    # --- Draw stop line ---\n", "    cv2.line(frame, (0, STOP_LINE_Y), (frame.shape[1], STOP_LINE_Y), (255, 0, 0), 2)\n", "\n", "    # --- Show signal status ---\n", "    cv2.putText(frame, f\"Signal: {signal_state}\", (50, 50),\n", "                cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)\n", "\n", "    out.write(frame)\n", "    cv2_imshow(frame)\n", "\n", "    if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'):\n", "        break\n", "\n", "cap.release()\n", "out.release()\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "C5QwULvzPqf7"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}