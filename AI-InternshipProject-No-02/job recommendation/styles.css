/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
}

.nav {
    position: absolute;
    top: 20px;
    left: 20px;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    text-decoration: none;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.back-btn:hover {
    background: #667eea;
    color: white;
    transform: translateX(-3px);
}

.header-content .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.header-content .logo i {
    font-size: 2.5rem;
    color: #667eea;
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
}

.subtitle {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 400;
}

/* Form Styles */
.form-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.recommendation-form h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.recommendation-form h3 i {
    color: #667eea;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group small {
    margin-top: 5px;
    color: #718096;
    font-size: 0.85rem;
}

/* Submit Button */
.submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Loading Spinner */
.loading {
    text-align: center;
    padding: 40px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    color: #4a5568;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.loading small {
    color: #718096;
    font-size: 0.9rem;
}

/* Results Container */
.results-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.results-header {
    text-align: center;
    margin-bottom: 30px;
}

.results-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.results-header h2 i {
    color: #667eea;
}

.results-header p {
    color: #718096;
    font-size: 1rem;
}

/* Career Results Styles */
.career-results {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.recommendation-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
}

.recommendation-card h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.career-title {
    font-size: 2rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
}

.career-subtitle {
    opacity: 0.9;
    font-size: 1rem;
}

.skills-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.skills-card {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    padding: 25px;
}

.skills-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.skills-card h3 i {
    color: #667eea;
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.skill-tag {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.skill-tag.technical {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border: 1px solid rgba(102, 126, 234, 0.3);
}

.skill-tag.soft {
    background: rgba(118, 75, 162, 0.1);
    color: #764ba2;
    border: 1px solid rgba(118, 75, 162, 0.3);
}

.skill-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.skills-note {
    color: #718096;
    font-size: 0.9rem;
    font-style: italic;
}

.advice-card {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    padding: 30px;
}

.advice-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.advice-card h3 i {
    color: #667eea;
}

.advice-text {
    color: #4a5568;
    line-height: 1.7;
    font-size: 1rem;
}

/* Job Results Styles */
.jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.job-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.5s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.job-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
    flex: 1;
}

.similarity-score {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 8px 12px;
    border-radius: 10px;
    min-width: 60px;
}

.score-label {
    font-size: 0.7rem;
    font-weight: 500;
    opacity: 0.9;
}

.score-value {
    font-size: 1.1rem;
    font-weight: 700;
}

.job-details p {
    margin: 8px 0;
    color: #718096;
    display: flex;
    align-items: center;
    gap: 8px;
}

.job-details i {
    color: #667eea;
    width: 16px;
}

.job-actions {
    margin-top: 20px;
    text-align: center;
}

.apply-btn {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.3);
}

/* Action Buttons */
.action-buttons,
.results-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.print-btn {
    background: #4299e1;
    color: white;
}

.print-btn:hover {
    background: #3182ce;
    transform: translateY(-2px);
}

.retry-btn {
    background: #ed8936;
    color: white;
}

.retry-btn:hover {
    background: #dd6b20;
    transform: translateY(-2px);
}

/* Error Message */
.error-message {
    text-align: center;
    padding: 40px;
    color: #e53e3e;
}

.error-message i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #fed7d7;
}

.error-message h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #e53e3e;
}

.error-message p {
    color: #718096;
    margin-bottom: 25px;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    .header-content .logo i {
        font-size: 2rem;
    }

    .form-container {
        padding: 25px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .jobs-grid {
        grid-template-columns: 1fr;
    }

    .skills-section {
        grid-template-columns: 1fr;
    }

    .nav {
        position: static;
        margin-bottom: 20px;
    }

    .action-buttons,
    .results-actions {
        flex-direction: column;
        align-items: center;
    }

    .action-btn {
        width: 200px;
        justify-content: center;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Footer */
.footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}
