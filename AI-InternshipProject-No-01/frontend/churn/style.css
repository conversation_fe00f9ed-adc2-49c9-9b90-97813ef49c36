* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Segoe UI', sans-serif;
  background: linear-gradient(to right, #f0f4f8, #e2ebf0);
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: auto;
  background: white;
  padding: 30px 40px;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

h1 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  font-size: 28px;
}

form .grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

input, select {
  padding: 12px;
  font-size: 15px;
  border-radius: 10px;
  border: 1px solid #ccc;
  transition: border 0.3s ease;
  width: 100%;
}

input:focus, select:focus {
  border-color: #007BFF;
  outline: none;
}

button {
  width: 100%;
  padding: 14px;
  background: #007BFF;
  color: white;
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background 0.3s;
}

button:hover {
  background: #0056b3;
}

.card {
  margin-top: 30px;
  padding: 20px 25px;
  border-left: 5px solid #007BFF;
  background-color: #f9fcff;
  border-radius: 12px;
}

.card h2 {
  margin-bottom: 15px;
  color: #007BFF;
}

.card p {
  font-size: 16px;
  margin: 10px 0;
}

@media (max-width: 500px) {
  .container {
    padding: 20px;
  }

  h1 {
    font-size: 24px;
  }
}
