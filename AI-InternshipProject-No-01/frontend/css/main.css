/* ===== GLOBAL STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Poppins', sans-serif;
}

:root {
  --primary-color: #007bff;
  --primary-dark: #0056b3;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --border-radius: 12px;
  --box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;
}

body {
  background: linear-gradient(135deg, var(--gray-100), var(--white));
  color: var(--dark-color);
  line-height: 1.6;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* ===== HEADER STYLES ===== */
.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 0;
}

.header h1 {
  font-size: 3rem;
  font-weight: 900;
  color: var(--dark-color);
  margin-bottom: 10px;
  letter-spacing: 1px;
}

.header p {
  font-size: 1.2rem;
  color: var(--gray-600);
  font-weight: 400;
}

/* ===== MAIN CONTENT ===== */
.main-content {
  margin-bottom: 60px;
}

/* ===== CARDS GRID ===== */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.card {
  background: var(--white);
  padding: 40px 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  text-align: center;
  transition: var(--transition);
  border: 1px solid var(--gray-200);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 15px;
}

.card p {
  color: var(--gray-600);
  margin-bottom: 25px;
  line-height: 1.5;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-block;
  padding: 12px 30px;
  background: var(--primary-color);
  color: var(--white);
  text-decoration: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition);
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.btn-secondary {
  background: var(--gray-600);
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
  background: var(--gray-700);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.btn-success {
  background: var(--success-color);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
  background: #218838;
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* ===== FORMS ===== */
.form-container {
  background: var(--white);
  padding: 40px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 30px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--dark-color);
}

input, select, textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  background: var(--white);
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* ===== RESULTS CARD ===== */
.results-container {
  background: var(--white);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-top: 30px;
}

.result-card {
  padding: 25px;
  background: var(--gray-100);
  border-radius: var(--border-radius);
  border-left: 5px solid var(--primary-color);
}

.result-card h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
}

.result-card p {
  margin: 10px 0;
  font-size: 1.1rem;
}

/* ===== FOOTER ===== */
.footer {
  text-align: center;
  padding: 30px 0;
  color: var(--gray-600);
  border-top: 1px solid var(--gray-200);
  margin-top: 60px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .header h1 {
    font-size: 2.5rem;
  }
  
  .header p {
    font-size: 1rem;
  }
  
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .card {
    padding: 30px 20px;
  }
  
  .form-container {
    padding: 25px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 2rem;
  }
  
  .card {
    padding: 25px 15px;
  }
  
  .form-container {
    padding: 20px;
  }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }
.mt-20 { margin-top: 20px; }
.mt-30 { margin-top: 30px; }
.hidden { display: none; }
.visible { display: block; }
