!pip install ultralytics opencv-python

import cv2
import os
import numpy as np
from ultralytics import <PERSON><PERSON><PERSON>
from google.colab.patches import cv2_imshow

# Load YOLO model
model = YOLO("yolov8n.pt")

# Input video
video_path = "/content/tr.mp4"   # apni video ka path do
cap = cv2.VideoCapture(video_path)

# Output video
fourcc = cv2.VideoWriter_fourcc(*'XVID')
out = cv2.VideoWriter("output.avi", fourcc, 30, (int(cap.get(3)), int(cap.get(4))))

# Folder for snapshots
os.makedirs("violations", exist_ok=True)

# ========================
# Traffic Signal Detection (your logic)
# ========================
# Update traffic_light_roi to be within frame boundaries
# Adjust values if needed
H, W = 800, 100  # Initialize H and W
roi_w, roi_h = 90, 160   # width, height of ROI box
roi_x = W - 60          # 100 px from right edge
roi_y = 0    # will be set later

traffic_light_roi = (roi_x, roi_y, roi_w, roi_h)

def get_light_color(frame):
    global traffic_light_roi, H, W # Declare as global

    H, W = frame.shape[:2]
    roi_x = W - 250       # 100 px from right edge
    roi_y = int(H/3) - 160  # roughly 1/3rd from top
    traffic_light_roi = (roi_x, roi_y, roi_w, roi_h)

    x, y, w, h = traffic_light_roi
    # Ensure ROI is within frame boundaries
    x = max(0, x)
    y = max(0, y)
    w = min(w, frame.shape[1] - x)
    h = min(h, frame.shape[0] - y)

    roi = frame[y:y+h, x:x+w]
    # Check if ROI is empty
    if roi.size == 0:
        return "UNKNOWN"

    hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)

    # Red range
    lower_red1 = np.array([0, 100, 100])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 100, 100])
    upper_red2 = np.array([180, 255, 255])

    # Yellow range
    lower_yellow = np.array([15, 100, 100])
    upper_yellow = np.array([35, 255, 255])

    # Green range
    lower_green = np.array([40, 50, 50])
    upper_green = np.array([90, 255, 255])

    # Create masks
    mask_red = cv2.inRange(hsv, lower_red1, upper_red1) | cv2.inRange(hsv, lower_red2, upper_red2)
    mask_yellow = cv2.inRange(hsv, lower_yellow, upper_yellow)
    mask_green = cv2.inRange(hsv, lower_green, upper_green)

    # Count pixels
    red_pixels = cv2.countNonZero(mask_red)
    yellow_pixels = cv2.countNonZero(mask_yellow)
    green_pixels = cv2.countNonZero(mask_green)

    if red_pixels > yellow_pixels and red_pixels > green_pixels and red_pixels > 50:
        return "RED"
    elif yellow_pixels > red_pixels and yellow_pixels > green_pixels and yellow_pixels > 50:
        return "YELLOW"
    elif green_pixels > red_pixels and green_pixels > yellow_pixels and green_pixels > 50:
        return "GREEN"
    else:
        return "UNKNOWN"
# ========================
# Main Loop
# ========================
STOP_LINE_Y = 700  # apni video ke hisaab se adjust karo
frame_count = 0
violation_count = 0
captured_ids = set()
while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    frame_count += 10

    # --- Detect traffic signal ---
    signal_state = get_light_color(frame)

    results = model.track(frame, persist=True, verbose=False)   # track instead of predict

    for r in results:
        for box in r.boxes:
            cls_id = int(box.cls[0])
            label = model.names[cls_id]

            if label in ["car", "truck", "bus", "motorbike"]:
                x1, y1, x2, y2 = map(int, box.xyxy[0])
                cy = (y1 + y2) // 2  # center Y

                color = (0, 255, 0)  # default safe

                # Tracking ID (unique for each vehicle)
                track_id = int(box.id[0]) if box.id is not None else None

                # 🚦 Violation only if RED and vehicle crosses line
                if signal_state == "RED" and cy < STOP_LINE_Y:
                    color = (0, 0, 255)

                    # ✅ Only save once per vehicle
                    if track_id is not None and track_id not in captured_ids:
                        violation_count += 1
                        captured_ids.add(track_id)  # remember this vehicle
                        cv2.imwrite(f"violations/violation_{violation_count}.jpg", frame)

                # Draw box + label
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                cv2.putText(frame, label, (x1, y1 - 5),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    # --- Draw ROI for traffic light ---
    x, y, w, h = traffic_light_roi
    cv2.rectangle(frame, (x, y), (x+w, y+h), (255, 255, 0), 2)

    # --- Draw stop line ---
    cv2.line(frame, (0, STOP_LINE_Y), (frame.shape[1], STOP_LINE_Y), (255, 0, 0), 2)

    # --- Show signal status ---
    cv2.putText(frame, f"Signal: {signal_state}", (50, 50),
                cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)

    out.write(frame)
    cv2_imshow(frame)

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
out.release()
cv2.destroyAllWindows()

