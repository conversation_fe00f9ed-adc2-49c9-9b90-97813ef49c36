* { box-sizing: border-box; }
:root {
  --bg: #0f172a;
  --card: #111827;
  --text: #e5e7eb;
  --muted: #9ca3af;
  --primary: #22c55e;
  --danger: #ef4444;
  --accent: #3b82f6;
}
body {
  margin: 0; font-family: system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif;
  background: linear-gradient(180deg, #0b1020, #0f172a);
  color: var(--text);
}
header { position: sticky; top: 0; backdrop-filter: blur(8px); background: rgba(15, 23, 42, 0.6); border-bottom: 1px solid #1f2937; }
header h1 { margin: 0; padding: 16px 24px; font-size: 20px; }
nav { padding: 0 16px 16px; display: flex; gap: 8px; }
nav .tab-btn { background: var(--card); color: var(--text); border: 1px solid #1f2937; border-radius: 8px; padding: 10px 14px; cursor: pointer; }
nav .tab-btn:hover { border-color: var(--accent); }

main { max-width: 1000px; margin: 24px auto; padding: 0 16px; }
.tab { display: none; }
.tab.active { display: block; }

.card { background: var(--card); border: 1px solid #1f2937; border-radius: 12px; padding: 16px; box-shadow: 0 10px 30px rgba(0,0,0,.3); }
.row { display: flex; gap: 10px; align-items: center; margin: 10px 0; }
.grid-2 { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; }
@media (max-width: 800px) { .grid-2 { grid-template-columns: 1fr; } }

input[type="text"], input[type="file"], button { width: 100%; padding: 10px 12px; border-radius: 8px; border: 1px solid #1f2937; background: #0b1220; color: var(--text); }
button { cursor: pointer; background: #0c162a; }
button:hover { border-color: var(--accent); }

video, canvas, img { width: 100%; max-width: 480px; border-radius: 8px; border: 1px solid #1f2937;  }
video { object-fit: contain; }
.video-wrapper video { display: block; z-index: 1; }

.video-wrapper { position: relative; display: inline-block; max-width: 480px; }
#overlay { position: absolute; left: 0; top: 0; width: 100%; height: 100%; pointer-events: none; background: transparent; z-index: 2; }

.status { margin-top: 8px; color: var(--muted); }
.muted { color: var(--muted); }

.face-box { position: absolute; border: 3px solid var(--danger); border-radius: 8px; }
.face-box.recognized { border-color: var(--primary); }

#attendance-table { width: 100%; border-collapse: collapse; margin-top: 10px; }
#attendance-table th, #attendance-table td { border: 1px solid #1f2937; padding: 8px; text-align: left; }
#attendance-table th { background: #111b2e; }

