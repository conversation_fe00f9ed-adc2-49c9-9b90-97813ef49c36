<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Job Recommendation Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .dashboard-container {
            max-width: 1000px;
            width: 90%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header .logo {
            font-size: 3rem;
            color: #667eea;
        }

        .header p {
            font-size: 1.2rem;
            color: #718096;
            font-weight: 400;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 40px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }

        .service-card {
            background: white;
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 3px solid transparent;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.5s;
        }

        .service-card:hover::before {
            left: 100%;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .service-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .service-card h2 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
        }

        .service-card p {
            color: #718096;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .service-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .service-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .features {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .feature {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #4a5568;
            font-size: 0.9rem;
        }

        .feature i {
            color: #667eea;
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid #e2e8f0;
            color: #718096;
        }

        @media (max-width: 768px) {
            .dashboard-container {
                width: 95%;
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .service-card {
                padding: 30px 20px;
            }

            .service-icon {
                font-size: 3rem;
            }

            .service-card h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="header">
            <h1>
                <i class="fas fa-robot logo"></i>
                AI Job Recommendation Dashboard
            </h1>
            <p>Choose your AI-powered career service</p>
        </div>

        <div class="services-grid">
            <!-- AI Career Advice Card -->
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <h2>AI Career Advice</h2>
                <p>Get personalized career recommendations based on your education and skills. Our AI analyzes your profile and suggests the best career path with skill development roadmap.</p>
                
                <div class="features">
                    <div class="feature">
                        <i class="fas fa-graduation-cap"></i>
                        <span>Education Analysis</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-chart-line"></i>
                        <span>Career Path</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-tools"></i>
                        <span>Skill Roadmap</span>
                    </div>
                </div>

                <a href="advice.html" class="service-btn">
                    <i class="fas fa-arrow-right"></i>
                    Get Career Advice
                </a>
            </div>

            <!-- AI Job Recommendation Card -->
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-briefcase"></i>
                </div>
                <h2>AI Job Recommendation</h2>
                <p>Find jobs that perfectly match your profile using advanced semantic analysis. Our AI understands your preferences and matches you with the most suitable opportunities.</p>

                <div class="features">
                    <div class="feature">
                        <i class="fas fa-search"></i>
                        <span>Smart Matching</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-percentage"></i>
                        <span>Match Score</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-filter"></i>
                        <span>Filtered Results</span>
                    </div>
                </div>

                <a href="jobs.html" class="service-btn">
                    <i class="fas fa-arrow-right"></i>
                    Find Jobs
                </a>
            </div>


        </div>

        <div class="footer">
            <p>&copy; 2024 AI Job Recommendation System. Powered by Machine Learning & Semantic Analysis.</p>
        </div>
    </div>
</body>
</html>
