/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
}

.header-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #667eea;
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
}

.subtitle {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 400;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Card Base Styles */
.upload-card,
.processing-card,
.results-card,
.error-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Upload Section */
.upload-header {
    text-align: center;
    margin-bottom: 30px;
}

.upload-header i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
}

.upload-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 10px;
}

.upload-header p {
    color: #718096;
    font-size: 1rem;
}

.upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 15px;
    padding: 60px 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 30px;
    background: rgba(247, 250, 252, 0.5);
}

.upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.upload-content i {
    font-size: 3rem;
    color: #a0aec0;
    margin-bottom: 20px;
}

.primary-text {
    color: #667eea;
    font-weight: 600;
}

.upload-text {
    font-size: 1.1rem;
    margin-bottom: 8px;
}

.upload-subtext {
    color: #a0aec0;
    font-size: 0.9rem;
}

/* File Info */
.file-info {
    background: rgba(102, 126, 234, 0.05);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-details i {
    font-size: 1.5rem;
    color: #667eea;
}

.file-text {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.file-name {
    font-weight: 600;
    color: #2d3748;
}

.file-size {
    font-size: 0.9rem;
    color: #718096;
}

.remove-file {
    background: none;
    border: none;
    color: #e53e3e;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.remove-file:hover {
    background: rgba(229, 62, 62, 0.1);
}

/* Buttons */
.process-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 30px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.process-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.process-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Processing Section */
.processing-animation {
    text-align: center;
    margin-bottom: 30px;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 30px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.processing-steps {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: 300px;
    margin: 0 auto;
}

.step {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    opacity: 0.5;
}

.step.active {
    opacity: 1;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.step i {
    width: 20px;
    text-align: center;
}

.processing-text {
    text-align: center;
    color: #718096;
    font-size: 1rem;
}

/* Results Section */
.results-header {
    text-align: center;
    margin-bottom: 30px;
}

.results-header i {
    font-size: 3rem;
    color: #48bb78;
    margin-bottom: 15px;
}

.results-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 10px;
}

.results-toolbar {
    display: flex;
    gap: 12px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.toolbar-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.toolbar-btn.secondary {
    background: #718096;
}

.toolbar-btn.secondary:hover {
    background: #4a5568;
}

.minutes-container {
    background: #f7fafc;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e2e8f0;
}

.minutes-content {
    white-space: pre-wrap;
    line-height: 1.8;
    color: #2d3748;
    font-size: 1rem;
    max-height: 500px;
    overflow-y: auto;
}

/* Error Section */
.error-header {
    text-align: center;
    margin-bottom: 25px;
}

.error-header i {
    font-size: 3rem;
    color: #e53e3e;
    margin-bottom: 15px;
}

.error-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2d3748;
}

.error-content {
    text-align: center;
}

.error-message {
    color: #e53e3e;
    margin-bottom: 25px;
    font-size: 1rem;
    padding: 15px;
    background: rgba(229, 62, 62, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(229, 62, 62, 0.2);
}

.retry-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.retry-btn:hover {
    background: #c53030;
    transform: translateY(-1px);
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #48bb78;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.error {
    border-left-color: #e53e3e;
}

.toast i {
    color: #48bb78;
}

.toast.error i {
    color: #e53e3e;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header-content,
    .upload-card,
    .processing-card,
    .results-card,
    .error-card {
        padding: 25px 20px;
    }

    .logo h1 {
        font-size: 2rem;
    }

    .upload-area {
        padding: 40px 20px;
    }

    .results-toolbar {
        flex-direction: column;
    }

    .toolbar-btn {
        justify-content: center;
    }

    .toast {
        min-width: auto;
        margin: 0 10px;
    }
}
