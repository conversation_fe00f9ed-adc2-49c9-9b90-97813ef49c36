* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dashboard-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 90%;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 40px;
}

.dashboard-header h1 {
    color: #333;
    font-size: 2.5rem;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.dashboard-header p {
    color: #666;
    font-size: 1.1rem;
}

.button-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.dashboard-btn {
    background: white;
    border: none;
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.dashboard-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.dashboard-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.dashboard-btn:hover::before {
    left: 100%;
}

.btn-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.dashboard-btn h3 {
    color: #333;
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.dashboard-btn p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.cv-generator:hover {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.career-advice:hover {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.job-recommendation:hover {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 20px;
    }
    
    .dashboard-header h1 {
        font-size: 2rem;
    }
    
    .button-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}